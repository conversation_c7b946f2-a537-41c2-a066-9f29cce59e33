# Rencana Implementasi Archive-Service dalam Event-Driven Architecture

## Overview

Dokumen ini menjelaskan rencana implementasi untuk menambahkan **archive-service** sebagai consumer dalam event-driven architecture yang sudah ada. Saat ini, ketika analysis worker se<PERSON><PERSON> beker<PERSON>, event `analysis.completed` sudah dikonsumsi oleh:
- **notification-service** - untuk mengirim notifikasi real-time via WebSocket
- **assessment-service** - untuk update status job dan refund tokens

Archive-service akan ditambahkan sebagai consumer ketiga untuk melakukan archiving otomatis hasil analisis.

## Analisis Arsitektur Saat Ini

### Event Flow yang Ada
```
analysis-worker (publisher)
    ↓ publishes to
atma_events_exchange (topic exchange)
    ↓ routes to queues
├── analysis_events_notifications (notification-service)
├── analysis_events_assessments (assessment-service)
└── [NEW] analysis_events_archive (archive-service)
```

### Event Types yang Dipublish
1. **analysis.completed** - Analisis berhasil diselesaikan
2. **analysis.failed** - Analisis gagal
3. **analysis.started** - Analisis dimulai (opsional)

### Event Data Structure
```json
{
  "eventType": "analysis.completed",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "jobId": "job_123",
  "userId": "user-uuid",
  "userEmail": "<EMAIL>",
  "resultId": "result-uuid",
  "metadata": {
    "assessmentName": "AI-Driven Talent Mapping",
    "processingTime": 5000,
    "retryCount": 0
  }
}
```

## Implementasi Archive-Service Event Consumer

### 1. Struktur File Baru

```
archive-service/
├── src/
│   ├── config/
│   │   └── rabbitmq.js          [NEW]
│   ├── services/
│   │   └── eventConsumer.js     [NEW]
│   └── app.js                   [MODIFY]
├── .env.example                 [MODIFY]
└── package.json                 [MODIFY]
```

### 2. Dependencies yang Diperlukan

Tambahkan ke `package.json`:
```json
{
  "dependencies": {
    "amqplib": "^0.10.3"
  }
}
```

### 3. Environment Variables

Tambahkan ke `.env.example`:
```env
# Event-Driven Architecture Configuration
RABBITMQ_URL=amqp://localhost:5672
EVENTS_EXCHANGE_NAME=atma_events_exchange
EVENTS_QUEUE_NAME_ARCHIVE=analysis_events_archive
CONSUMER_PREFETCH=10
```

### 4. RabbitMQ Configuration

File: `src/config/rabbitmq.js`
- Setup connection ke RabbitMQ
- Assert events exchange (topic)
- Assert archive queue dengan DLQ
- Bind queue ke routing keys

### 5. Event Consumer Service

File: `src/services/eventConsumer.js`
- Initialize RabbitMQ connection
- Start/stop consuming events
- Process events berdasarkan type
- Handle analysis.completed events

### 6. Event Handlers

#### handleAnalysisCompleted
Fungsi ini akan:
1. **Validate event data** - pastikan semua field required ada
2. **Check if result exists** - cek apakah result sudah ada di database
3. **Archive metadata** - simpan metadata tambahan untuk archiving
4. **Update job status** - update status job di archive database
5. **Log archiving activity** - audit trail untuk archiving

#### handleAnalysisFailed
Fungsi ini akan:
1. **Log failure event** - catat event kegagalan
2. **Update job status** - mark job sebagai failed
3. **Store error information** - simpan detail error untuk debugging

### 7. Integration dengan Archive Service

Archive-service sudah memiliki:
- ✅ Database models (AnalysisResult, AnalysisJob)
- ✅ Service layer untuk CRUD operations
- ✅ Internal service authentication

Yang perlu ditambahkan:
- 🆕 Event consumer untuk otomatis archiving
- 🆕 Enhanced logging untuk event processing
- 🆕 Error handling untuk event failures

## Keuntungan Implementasi

### 1. Automated Archiving
- **Otomatis**: Hasil analisis langsung diarsipkan tanpa manual intervention
- **Real-time**: Archiving terjadi segera setelah analisis selesai
- **Reliable**: Menggunakan message queue untuk reliability

### 2. Decoupled Architecture
- **Independence**: Archive-service tidak bergantung pada analysis-worker
- **Scalability**: Bisa menambah multiple archive consumers
- **Maintainability**: Perubahan di satu service tidak affect yang lain

### 3. Enhanced Data Management
- **Consistency**: Semua hasil analisis pasti terarsip
- **Audit Trail**: Complete logging untuk semua archiving activities
- **Recovery**: Bisa replay events jika ada masalah archiving

## Tahapan Implementasi

### Phase 1: Setup Infrastructure (1-2 hari)
1. ✅ Install RabbitMQ dependencies
2. ✅ Create RabbitMQ configuration
3. ✅ Setup event consumer service
4. ✅ Add environment variables

### Phase 2: Event Handlers (2-3 hari)
1. ✅ Implement handleAnalysisCompleted
2. ✅ Implement handleAnalysisFailed
3. ✅ Add comprehensive error handling
4. ✅ Add logging dan monitoring

### Phase 3: Integration & Testing (2-3 hari)
1. ✅ Integrate dengan existing archive service
2. ✅ Unit testing untuk event handlers
3. ✅ Integration testing dengan RabbitMQ
4. ✅ End-to-end testing dengan analysis flow

### Phase 4: Deployment & Monitoring (1 hari)
1. ✅ Deploy ke development environment
2. ✅ Monitor event processing
3. ✅ Performance testing
4. ✅ Production deployment

## Considerations & Best Practices

### 1. Error Handling
- **Retry Logic**: Implement exponential backoff untuk failed events
- **Dead Letter Queue**: Events yang gagal diproses masuk DLQ
- **Alerting**: Monitor DLQ untuk failed events

### 2. Performance
- **Batch Processing**: Consider batching untuk high-volume scenarios
- **Connection Pooling**: Reuse database connections
- **Memory Management**: Proper cleanup untuk long-running consumers

### 3. Monitoring
- **Metrics**: Track event processing rate, success/failure rates
- **Logging**: Structured logging untuk debugging
- **Health Checks**: Monitor consumer health dan connection status

### 4. Data Consistency
- **Idempotency**: Handle duplicate events gracefully
- **Validation**: Validate event data sebelum processing
- **Rollback**: Mechanism untuk rollback jika ada error

## Testing Strategy

### 1. Unit Tests
- Test event handlers dengan mock data
- Test error scenarios
- Test validation logic

### 2. Integration Tests
- Test dengan real RabbitMQ instance
- Test database operations
- Test end-to-end event flow

### 3. Load Tests
- Test dengan high-volume events
- Test consumer performance
- Test system stability

## Rollback Plan

Jika implementasi bermasalah:
1. **Disable event consumer** - stop consuming events
2. **Fallback to manual archiving** - temporary manual process
3. **Fix issues** - debug dan fix problems
4. **Re-enable consumer** - restart event processing

## Success Metrics

1. **Event Processing Rate**: > 95% events processed successfully
2. **Latency**: < 5 seconds dari event publish ke archiving complete
3. **Error Rate**: < 1% event processing failures
4. **Uptime**: > 99.9% consumer availability

## Next Steps

1. **Review dan approval** rencana implementasi
2. **Setup development environment** dengan RabbitMQ
3. **Start implementation** mengikuti tahapan yang sudah direncanakan
4. **Regular progress review** dan adjustment jika diperlukan
